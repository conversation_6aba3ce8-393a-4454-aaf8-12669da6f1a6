package com.allin.silas.event.infra.repository;

import com.allin.silas.event.adapter.query.EventAdsBQuery;
import com.allin.silas.event.adapter.vo.EventAdsBPageVo;
import com.allin.silas.event.app.entity.EventAdsB;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EventAdsBMapper extends BaseMapper<EventAdsB> {
    /**
     * 分页查询
     */
    Page<EventAdsBPageVo> page(Page<EventAdsBPageVo> page,
                               @Param("tableName") String tableName,
                               @Param("query") EventAdsBQuery query);

    /**
     * 分页查询某条ADS-B信息事件
     */
    Page<EventAdsBPageVo> pageByCallsign(Page<EventAdsBPageVo> page,
                                         @Param("tableName") String tableName,
                                         @Param("projectId") String projectId,
                                         @Param("callsign") String callsign);

    /**
     * 获取按 callsign 和 adsBStatus 的组合
     */
    List<EventAdsBPageVo> listGroupByCallsignAndStatus(@Param("tableName") String tableName,
                                                       @Param("query") EventAdsBQuery query);

    /**
     * 根据 callsign 和 adsBStatus 获取最新记录
     */
    EventAdsBPageVo getLatestRecordByCallsignAndStatus(@Param("tableName") String tableName,
                                                       @Param("projectId") String projectId,
                                                       @Param("callsign") String callsign,
                                                       @Param("adsBStatus") Integer adsBStatus);
}