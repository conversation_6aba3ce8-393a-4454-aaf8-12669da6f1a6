package com.allin.silas.event.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.event.adapter.query.EventAdsBQuery;
import com.allin.silas.event.adapter.vo.EventAdsBPageVo;
import com.allin.silas.event.app.service.EventAdsBQueryService;
import com.allin.silas.event.infra.repository.EventAdsBMapper;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * AdsB查询服务实现
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Service
public class EventAdsBQueryServiceImpl implements EventAdsBQueryService {

    private final EventAdsBMapper eventAdsBMapper;

    public EventAdsBQueryServiceImpl(EventAdsBMapper eventAdsBMapper) {
        this.eventAdsBMapper = eventAdsBMapper;
    }

    @Override
    public PageData<EventAdsBPageVo> page(PageParam pageParam, EventAdsBQuery query) {
        String tableName = TableNameUtils.getAdsBInfo(query.getStartTime().toLocalDate());

        // 第一步：获取所有符合条件的唯一 callsign 和 adsBStatus 组合
        List<EventAdsBPageVo> groups = eventAdsBMapper.listGroupByCallsignAndStatus(tableName, query);

        // 如果没有数据，返回空分页结果
        if (CollUtil.isEmpty(groups)) {
            return PageData.empty();
        }

        // 第二步：对每个唯一组合，查询最新时间的完整数据
        List<EventAdsBPageVo> allResults = new ArrayList<>();
        for (EventAdsBPageVo combination : groups) {
            EventAdsBPageVo latestRecord = eventAdsBMapper.getLatestRecordByCallsignAndStatus(
                    tableName, query.getProjectId(), combination.getCallsign(), combination.getAdsBStatus());
            if (latestRecord != null) {
                allResults.add(latestRecord);
            }
        }

        // 第三步：按创建时间倒序排序
        allResults.sort((a, b) -> b.getCreatedTime().compareTo(a.getCreatedTime()));

        // 第四步：手动分页
        List<EventAdsBPageVo> pagedResults = ListUtil.sub(allResults, (int) pageParam.getOffset(), (int) pageParam.getSize());

        return PageData.getInstance(pageParam, pagedResults, (long) allResults.size());
    }


    @Override
    public Page<EventAdsBPageVo> pageByCallsign(PageParam pageParam, LocalDate date, String callsign) {
        String tableName = TableNameUtils.getAdsBInfo(date);
        String projectId = SecurityContextHolder.getProjectId();
        final Page<EventAdsBPageVo> page = pageParam.toPage();
        return eventAdsBMapper.pageByCallsign(page, tableName, projectId, callsign);
    }

    @Override
    public List<EventAdsBPageVo> listByCallsign(LocalDate date, String callsign) {
        String tableName = TableNameUtils.getAdsBInfo(date);
        String projectId = SecurityContextHolder.getProjectId();
        final Page<EventAdsBPageVo> page = Page.of(-1, -1);
        return eventAdsBMapper.pageByCallsign(page, tableName, projectId, callsign).getRecords();
    }
}
