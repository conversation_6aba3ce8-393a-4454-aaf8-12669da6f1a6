package com.allin.silas.event.app.service;

import com.allin.silas.event.adapter.query.EventAdsBQuery;
import com.allin.silas.event.adapter.vo.EventAdsBPageVo;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDate;
import java.util.List;

/**
 * AdsB查询服务
 *
 * <AUTHOR>
 * @since 2025/7/5
 */
public interface EventAdsBQueryService {

    /**
     * 分页查询
     */
    PageData<EventAdsBPageVo> page(PageParam pageParam, EventAdsBQuery query);

    /**
     * 按 callsign 分页查询
     */
    Page<EventAdsBPageVo> pageByCallsign(PageParam pageParam,
                                         LocalDate date,
                                         String callsign);

    /**
     * 按 callsign 分页查询
     */
    List<EventAdsBPageVo> listByCallsign(LocalDate date,
                                         String callsign);
}
