package com.allin.silas.visual.infra.repository;

import com.allin.silas.visual.adapter.vo.VisualTargetNeedToMergeVo;
import com.allin.silas.visual.adapter.vo.VisualTargetTraceVo;
import com.allin.silas.visual.app.entity.VisualTargetOriginalInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VisualTargetOriginalInfoMapper extends BaseMapper<VisualTargetOriginalInfo> {

    /**
     * 目标轨迹列表
     */
    List<VisualTargetTraceVo> trace(@Param("tableName") String tableName,
                                    @Param("batchNumber") String batchNumber);

    /**
     * 查询还没有合并完全的数据
     */
    List<VisualTargetNeedToMergeVo> listNeedToMerge(@Param("tableName") String tableName,
                                                    @Param("batchNumbers") List<String> batchNumbers);

    /**
     * 查询目标的切片地址列表
     */
    List<String> listImgPath(@Param("tableName") String tableName,
                             @Param("batchNumbers") List<String> batchNumbers);
}