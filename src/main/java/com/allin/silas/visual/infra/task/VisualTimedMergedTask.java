package com.allin.silas.visual.infra.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.allin.silas.common.mybatis.config.MybatisDynamicTableNameHandler;
import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.adapter.vo.VisualTargetNeedToMergeVo;
import com.allin.silas.visual.adapter.vo.VisualTargetNotMergeBaseInfo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.allin.silas.visual.infra.repository.VisualTargetMergedInfoMapper;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import com.allin.silas.visual.utils.VisualDetectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时合并数据
 *
 * <AUTHOR>
 * @since 2025/7/31
 */
@Slf4j
@Component
public class VisualTimedMergedTask {

    private final VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper;

    private final VisualTargetMergedInfoMapper visualTargetMergedInfoMapper;

    private final VisualDetectUtils visualDetectUtils;

    public VisualTimedMergedTask(VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper,
                                 VisualTargetMergedInfoMapper visualTargetMergedInfoMapper,
                                 VisualDetectUtils visualDetectUtils) {
        this.visualTargetOriginalInfoMapper = visualTargetOriginalInfoMapper;
        this.visualTargetMergedInfoMapper = visualTargetMergedInfoMapper;
        this.visualDetectUtils = visualDetectUtils;
    }


    /**
     * 每30秒合并一次数据
     */
    @Scheduled(cron = "*/30 * * * * *")
    @Transactional(rollbackFor = Exception.class)
    public void merged() {
        try {
            doMerged();
        } catch (Exception e) {
            log.error("可视目标合并数据任务执行失败", e);
            throw e;
        }
    }

    /**
     * 执行合并逻辑
     */
    private void doMerged() {
        long startTime = System.currentTimeMillis();
        LocalDate localDate = LocalDate.now();
        String mergedTableName = TableNameUtils.getVisualTargetMergedInfo(localDate);
        String originalTableName = TableNameUtils.getVisualTargetOriginalInfo(localDate);
        // 查询需要合并的数据
        final List<VisualTargetNotMergeBaseInfo> needMergedInfos = visualTargetMergedInfoMapper.listNotMerged(mergedTableName);

        if (CollUtil.isEmpty(needMergedInfos)) {
            log.debug("没有需要合并的数据，跳过合并任务");
            return;
        }

        final List<String> batchNumbers = needMergedInfos.stream().map(VisualTargetNotMergeBaseInfo::getBackendBatchNumber).toList();
        // 查询所有需要合并的原始数据
        final List<VisualTargetNeedToMergeVo> needToMergeVos = visualTargetOriginalInfoMapper.listNeedToMerge(originalTableName, batchNumbers);
        // 按目标编号分组
        final Map<String, List<VisualTargetNeedToMergeVo>> batchNumberMap = needToMergeVos.stream()
                .collect(Collectors.groupingBy(VisualTargetNeedToMergeVo::getBackendBatchNumber));

        List<VisualTargetMergedInfo> mergedInfos = new ArrayList<>();

        for (VisualTargetNotMergeBaseInfo mergedBaseInfo : needMergedInfos) {
            if (!batchNumberMap.containsKey(mergedBaseInfo.getBackendBatchNumber())) {
                continue;
            }
            List<VisualTargetNeedToMergeVo> originalInfos = batchNumberMap.get(mergedBaseInfo.getBackendBatchNumber());
            if (CollUtil.isEmpty(originalInfos)) {
                continue;
            }

            final VisualTargetMergedInfo mergedInfo = BeanUtil.copyProperties(mergedBaseInfo, VisualTargetMergedInfo.class);
            mergedInfos.add(mergedInfo);

            mergedInfo.setMergedTime(LocalDateTime.now());

            // 判断合并数量，相等则表示没有新轨迹，只需要更新合并状态和时间，而无需重新计算统计数据
            if (mergedInfo.getMergedCount() != null && mergedInfo.getMergedCount().equals(originalInfos.size())) {
                mergedInfo.setMergedStatus(1);
                continue;
            }

            // 更新合并数量
            mergedInfo.setMergedCount(originalInfos.size());

            // 按时间排序，第一个是最新的，最后一个是最早的
            originalInfos.sort(Comparator.comparing(VisualTargetNeedToMergeVo::getCreatedTime).reversed());

            // 更新开始和结束时间
            mergedInfo.setStartTime(originalInfos.get(originalInfos.size() - 1).getCreatedTime());
            mergedInfo.setEndTime(originalInfos.get(0).getCreatedTime());

            // 投票更新识别类型
            final Optional<String> detectSubTypeOpt = VisualDetectUtils.vote(originalInfos.stream()
                    .map(VisualTargetNeedToMergeVo::getDetectSubType)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            detectSubTypeOpt.ifPresent(detectSubType -> {
                mergedInfo.setDetectSubType(detectSubType);
                mergedInfo.setDetectType(visualDetectUtils.getDetectType(detectSubType, originalInfos.size()));
            });

            // 结束位置（最新时间）
            VisualTargetNeedToMergeVo endInfo = originalInfos.get(0);
            mergedInfo.setEndLongitude(endInfo.getLongitude());
            mergedInfo.setEndLatitude(endInfo.getLatitude());

            // 开始位置（最早时间）
            VisualTargetNeedToMergeVo startInfo = originalInfos.get(originalInfos.size() - 1);
            mergedInfo.setStartLongitude(startInfo.getLongitude());
            mergedInfo.setStartLatitude(startInfo.getLatitude());

            // 更新各种统计值
            updateStatisticalValues(mergedInfo, originalInfos);

            // 更新图片URL
            mergedInfo.setEndImgUrl(originalInfos.get(0).getImgUrl());
            mergedInfo.setStartImgUrl(originalInfos.get(originalInfos.size() - 1).getImgUrl());

            // 更新飞行方向
            mergedInfo.setEndFlightDirection(originalInfos.get(0).getFlightDirection());
            mergedInfo.setStartFlightDirection(originalInfos.get(originalInfos.size() - 1).getFlightDirection());
        }
        // 更新合并数据
        try {
            MybatisDynamicTableNameHandler.setSuffix(localDate);
            // 逐个更新每个合并信息
            visualTargetMergedInfoMapper.updateById(mergedInfos);
        } finally {
            MybatisDynamicTableNameHandler.clear();
        }

        long endTime = System.currentTimeMillis();
        log.info("可视目标合并数据任务完成，处理了 {} 条记录，耗时 {} ms", needMergedInfos.size(), endTime - startTime);
    }

    /**
     * 更新各种统计值
     */
    private void updateStatisticalValues(VisualTargetMergedInfo mergedInfo, List<VisualTargetNeedToMergeVo> originalInfos) {
        if (CollUtil.isEmpty(originalInfos)) {
            return;
        }

        // 初始化统计变量
        Float minAzimuth = null, maxAzimuth = null;
        Float minPitch = null, maxPitch = null;
        Float minDistance = null, maxDistance = null;
        Float minHeight = null, maxHeight = null;
        Float minSpeed = null, maxSpeed = null;
        Float minWingSpan = null, maxWingSpan = null;
        Float maxArea = null;
        Float maxConfidenceLevel = null;
        Double minRunwayDistance = null, maxRunwayDistance = null;
        Double minRunwayCenterDistance = null, maxRunwayCenterDistance = null;
        Integer maxTargetCount = null;
        Integer maxTargetSize = null;

        // 单次遍历计算所有统计值
        for (VisualTargetNeedToMergeVo info : originalInfos) {
            // 方位值
            if (info.getAzimuth() != null) {
                minAzimuth = (minAzimuth == null) ? info.getAzimuth() : Math.min(minAzimuth, info.getAzimuth());
                maxAzimuth = (maxAzimuth == null) ? info.getAzimuth() : Math.max(maxAzimuth, info.getAzimuth());
            }

            // 俯仰值
            if (info.getPitch() != null) {
                minPitch = (minPitch == null) ? info.getPitch() : Math.min(minPitch, info.getPitch());
                maxPitch = (maxPitch == null) ? info.getPitch() : Math.max(maxPitch, info.getPitch());
            }

            // 距离
            if (info.getDistance() != null) {
                minDistance = (minDistance == null) ? info.getDistance() : Math.min(minDistance, info.getDistance());
                maxDistance = (maxDistance == null) ? info.getDistance() : Math.max(maxDistance, info.getDistance());
            }

            // 高度
            if (info.getHeight() != null) {
                minHeight = (minHeight == null) ? info.getHeight() : Math.min(minHeight, info.getHeight());
                maxHeight = (maxHeight == null) ? info.getHeight() : Math.max(maxHeight, info.getHeight());
            }

            // 速度
            if (info.getSpeed() != null) {
                minSpeed = (minSpeed == null) ? info.getSpeed() : Math.min(minSpeed, info.getSpeed());
                maxSpeed = (maxSpeed == null) ? info.getSpeed() : Math.max(maxSpeed, info.getSpeed());
            }

            // 翼展
            if (info.getWingSpan() != null) {
                minWingSpan = (minWingSpan == null) ? info.getWingSpan() : Math.min(minWingSpan, info.getWingSpan());
                maxWingSpan = (maxWingSpan == null) ? info.getWingSpan() : Math.max(maxWingSpan, info.getWingSpan());
            }

            // 面积（只需最大值）
            if (info.getArea() != null) {
                maxArea = (maxArea == null) ? info.getArea() : Math.max(maxArea, info.getArea());
            }

            // 置信度（只需最大值）
            if (info.getConfidenceLevel() != null) {
                maxConfidenceLevel = (maxConfidenceLevel == null) ? info.getConfidenceLevel() : Math.max(maxConfidenceLevel, info.getConfidenceLevel());
            }

            // 目标数量（只需最大值）
            if (info.getTargetCount() != null) {
                maxTargetCount = (maxTargetCount == null) ? info.getTargetCount() : Math.max(maxTargetCount, info.getTargetCount());
            }

            // 跑道距离
            if (info.getRunwayDistance() != null) {
                minRunwayDistance = (minRunwayDistance == null) ? info.getRunwayDistance() : Math.min(minRunwayDistance, info.getRunwayDistance());
                maxRunwayDistance = (maxRunwayDistance == null) ? info.getRunwayDistance() : Math.max(maxRunwayDistance, info.getRunwayDistance());
            }

            // 跑道中心距离
            if (info.getRunwayCenterDistance() != null) {
                minRunwayCenterDistance = (minRunwayCenterDistance == null) ? info.getRunwayCenterDistance() : Math.min(minRunwayCenterDistance, info.getRunwayCenterDistance());
                maxRunwayCenterDistance = (maxRunwayCenterDistance == null) ? info.getRunwayCenterDistance() : Math.max(maxRunwayCenterDistance, info.getRunwayCenterDistance());
            }

            maxTargetSize = (maxTargetSize == null) ? info.getTargetSize() : Math.max(maxTargetSize, info.getTargetSize());
        }

        // 设置计算结果
        mergedInfo.setTargetSize(maxTargetSize);
        mergedInfo.setMinAzimuth(minAzimuth);
        mergedInfo.setMaxAzimuth(maxAzimuth);
        mergedInfo.setMinPitch(minPitch);
        mergedInfo.setMaxPitch(maxPitch);
        mergedInfo.setMinDistance(minDistance);
        mergedInfo.setMaxDistance(maxDistance);
        mergedInfo.setMinHeight(minHeight);
        mergedInfo.setMaxHeight(maxHeight);
        mergedInfo.setMinSpeed(minSpeed);
        mergedInfo.setMaxSpeed(maxSpeed);
        mergedInfo.setMinWingSpan(minWingSpan);
        mergedInfo.setMaxWingSpan(maxWingSpan);
        mergedInfo.setMaxArea(maxArea);
        mergedInfo.setMaxConfidenceLevel(maxConfidenceLevel);
        mergedInfo.setMaxTargetCount(maxTargetCount);
        mergedInfo.setMinRunwayDistance(minRunwayDistance);
        mergedInfo.setMaxRunwayDistance(maxRunwayDistance);
        mergedInfo.setMinRunwayCenterDistance(minRunwayCenterDistance);
        mergedInfo.setMaxRunwayCenterDistance(maxRunwayCenterDistance);
    }
}
