package com.allin.silas.visual.infra.repository;

import com.allin.silas.visual.adapter.dto.VisualTargetMarkDto;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoListQuery;
import com.allin.silas.visual.adapter.query.VisualTargetMergedInfoPageQuery;
import com.allin.silas.visual.adapter.vo.VisualTargetInfoListVo;
import com.allin.silas.visual.adapter.vo.VisualTargetMergedInfoVo;
import com.allin.silas.visual.adapter.vo.VisualTargetNotMergeBaseInfo;
import com.allin.silas.visual.app.entity.VisualTargetMergedInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VisualTargetMergedInfoMapper extends BaseMapper<VisualTargetMergedInfo> {
    /**
     * 分页查询
     */
    Page<VisualTargetInfoListVo> page(Page<VisualTargetInfoListVo> page,
                                      @Param("tableName") String tableName,
                                      @Param("query") VisualTargetMergedInfoPageQuery query);

    /**
     * 查询列表
     */
    List<VisualTargetInfoListVo> list(@Param("tableName") String tableName,
                                      @Param("query") VisualTargetMergedInfoListQuery query);

    /**
     * 查询没有合并的列表
     */
    List<VisualTargetNotMergeBaseInfo> listNotMerged(@Param("tableName") String tableName);

    /**
     * 获取详情
     */
    VisualTargetMergedInfoVo info(@Param("tableName") String tableName,
                                  @Param("backendBatchNumber") String backendBatchNumber);

    /**
     * 修改标记类型
     */
    Integer updateMark(@Param("tableName") String tableName,
                       @Param("mark") VisualTargetMarkDto markDto);

    /**
     * 修改标记类型
     */
    Integer setPushed(@Param("tableName") String tableName,
                      @Param("batchNumber") String batchNumber);
}