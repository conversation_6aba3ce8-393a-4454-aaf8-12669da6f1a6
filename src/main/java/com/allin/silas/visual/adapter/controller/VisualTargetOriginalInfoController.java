package com.allin.silas.visual.adapter.controller;

import com.allin.silas.visual.adapter.vo.VisualTargetTraceVo;
import com.allin.silas.visual.app.service.VisualTargetInfoQueryService;
import com.allin.view.base.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * 探测识别/可视目标
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/visual/target/original")
public class VisualTargetOriginalInfoController {

    private final VisualTargetInfoQueryService infoQueryService;

    public VisualTargetOriginalInfoController(VisualTargetInfoQueryService infoQueryService) {
        this.infoQueryService = infoQueryService;
    }

    /**
     * 查询可视目标轨迹
     *
     * @param date        yyyy-MM-dd
     * @param batchNumber 批次号
     */
    @GetMapping("/trace")
    public Result<List<VisualTargetTraceVo>> trace(@RequestParam LocalDate date,
                                                   @RequestParam String batchNumber) {
        return Result.ok(infoQueryService.trace(date, batchNumber));
    }

}
