package com.allin.silas.visual.app.manager.impl;

import com.allin.silas.common.util.database.TableNameUtils;
import com.allin.silas.visual.adapter.vo.VisualTargetTraceVo;
import com.allin.silas.visual.app.manager.VisualTargetOriginalInfoManager;
import com.allin.silas.visual.infra.repository.VisualTargetOriginalInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 可视目标原始信息通用逻辑层接口
 *
 * <AUTHOR>
 * @since 2025/6/27
 */
@Slf4j
@Service
public class VisualTargetOriginalInfoManagerImpl implements VisualTargetOriginalInfoManager {

    private final VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper;

    public VisualTargetOriginalInfoManagerImpl(VisualTargetOriginalInfoMapper visualTargetOriginalInfoMapper) {
        this.visualTargetOriginalInfoMapper = visualTargetOriginalInfoMapper;
    }

    @Override
    public List<VisualTargetTraceVo> trace(LocalDate date, String batchNumber) {
        String tableName = TableNameUtils.getVisualTargetMergedInfo(date);
        return visualTargetOriginalInfoMapper.trace(tableName, batchNumber);
    }

    @Override
    public List<String> listImgPath(LocalDate date, List<String> batchNumbers) {
        String tableName = TableNameUtils.getVisualTargetMergedInfo(date);
        return visualTargetOriginalInfoMapper.listImgPath(tableName, batchNumbers);
    }
}
