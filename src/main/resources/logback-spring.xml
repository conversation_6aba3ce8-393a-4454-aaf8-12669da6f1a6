<?xml version="1.0" encoding="UTF-8"?>
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<configuration scan="true" scanPeriod="30 seconds">
    <!--引入默认的配置-->
    <include resource="logback-default.xml"/>
    <!--自定义配置，比如你需要将某个包下的日志级别更改为Debug，就直接按Logback配置文件的规则来写就好了-->
    <!-- 测试环境配置，打印 DEBUG 级别日志 -->
    <springProfile name="test">
        <logger name="com.allin" level="DEBUG"/>
    </springProfile>
    <!-- 生产环境配置，默认只打印 INFO 及以上级别日志 -->
    <springProfile name="!test">
        <logger name="com.allin" level="INFO"/>
    </springProfile>
</configuration>